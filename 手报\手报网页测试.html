<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报警器状态</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .status {
            padding: 10px;
            font-size: 20px;
            border-radius: 5px;
        }
        .alert {
            background-color: red;
            color: white;
        }
        .no-alert {
            background-color: green;
            color: white;
        }
    </style>
</head>
<body>

    <h1>手动报警器状态</h1>
    <div id="alertStatus" class="status">
        正在加载...
    </div>

    <script>
        async function checkAlertStatus() {
            try {
                const response = await fetch('http://************:5005/status');
                const data = await response.json();
                const alertStatus = data.alert;

                const statusElement = document.getElementById('alertStatus');
                if (alertStatus) {
                    statusElement.textContent = '警报已触发！';
                    statusElement.classList.add('alert');
                    statusElement.classList.remove('no-alert');
                } else {
                    statusElement.textContent = '没有触发警报。';
                    statusElement.classList.add('no-alert');
                    statusElement.classList.remove('alert');
                }
            } catch (error) {
                console.error('无法获取报警器状态:', error);
            }
        }

        // 每秒检查一次状态
        setInterval(checkAlertStatus, 1000);
    </script>

</body>
</html>
