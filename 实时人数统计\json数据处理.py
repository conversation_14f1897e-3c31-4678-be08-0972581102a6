#!/usr/bin/env python
# coding: utf-8

# In[15]:


import json
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.patches import PathPatch
from matplotlib.path import Path
from shapely.geometry import Polygon, LineString, MultiLineString
from matplotlib.patches import Polygon as MplPolygon
from shapely.geometry import box
import math
from shapely.geometry import Point, Polygon


# In[12]:


plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


#   

#   

#   

# ### 不进行任何结构修改，仅简化

# In[3]:


input_path = r"C:\Users\<USER>\Desktop\疏散指示系统\管理学院_编辑版2222.json"
output_path = r"C:\Users\<USER>\Desktop\疏散指示系统\管理学院_仅简化.json"

with open(input_path, "r", encoding="utf-8") as f:
    data = json.load(f)

building = data["map"]["building"]

new_building = {
    "id": building.get("id"),
    "userId": building.get("userId"),
    "name": building.get("name"),
    "floors": []
}

for floor in building.get("floors", []):
    plane_str = floor.get("object", {}).get("plane_geometry", "")
    try:
        plane_json = json.loads(plane_str)
        points = [[pt["x"], pt["y"]] for pt in plane_json.get("points", [])]
        holes = [[pt["x"], pt["y"]] for pt in plane_json.get("holes", [])]
    except (json.JSONDecodeError, TypeError, KeyError):
        points = []
        holes = []

    new_floor = {
        "id": floor.get("id"),
        "userId": floor.get("userId"),
        "name": floor.get("name"),
        "points": points,
        "holes": holes,
        "blocks": [],
        "vertices": [],
        "road": {
            "edges": floor.get("road", {}).get("edges", {})
        }
    }

    # ✅ 解析 blocks 中的 points（JSON 字符串）
    for block in floor.get("blocks", []):
        block_obj = block.get("object", {})
        raw_points = block_obj.get("points", "[]")
        try:
            parsed_points = json.loads(raw_points)
            flat_points = [[pt["x"], pt["y"]] for pt in parsed_points]
        except (json.JSONDecodeError, TypeError):
            flat_points = []

        new_floor["blocks"].append({
            "id": block.get("id"),
            "points": flat_points,
            "rgb": block_obj.get("color"),
            "opacity": block_obj.get("opacity"),
            "height": block_obj.get("depth")
        })

    # ✅ 解析 vertices
    # ✅ 解析 vertices（position 是字符串，需提取 x, y）
    for vertex in floor.get("road", {}).get("vertices", []):
        vertex_obj = vertex.get("object", {})

        raw_position = vertex.get("position")
        if isinstance(raw_position, str):
            try:
                pos_obj = json.loads(raw_position)
                position = [pos_obj["x"], pos_obj["y"]]
            except (json.JSONDecodeError, TypeError, KeyError):
                position = []
        else:
            position = raw_position  # 如果不是字符串则保留原样（已是 [x, y]）

        new_floor["vertices"].append({
            "id": vertex.get("id"),
            "uid": vertex.get("userId"),
            "name": vertex.get("name"),
            "position": position,
            "rgb": vertex_obj.get("color"),
            "levels": {
                "level1": vertex_obj.get("type"),
                "level2": None,
                "level3": None
            }
        })

    new_building["floors"].append(new_floor)

# 添加 passages
# ✅ 简化 passages 格式
simplified_passages = []
for passage in building.get("passages", []):
    try:
        vertices = json.loads(passage.get("vertices", "[]"))
        if isinstance(vertices, list) and len(vertices) >= 2:
            simplified_passages.append({
                "v1": vertices[0],
                "v2": vertices[1],
                "type": passage.get("type")
            })
    except json.JSONDecodeError:
        continue  # 跳过解析失败的

new_building["passages"] = simplified_passages




# 封装输出
output_data = {"map": {"building": new_building}}

# 写入结果
with open(output_path, "w", encoding="utf-8") as f:
    json.dump(output_data, f, indent=2, ensure_ascii=False)

print(f"✅ JSON 已成功转换并保存至：{output_path}")


# ### 边结构修改

# In[13]:


def parse_points(json_str):
    try:
        pts = json.loads(json_str)
        return [[pt["x"], pt["y"]] for pt in pts]
    except:
        return []

def parse_geometry(json_str):
    try:
        geo = json.loads(json_str)
        points = [[pt["x"], pt["y"]] for pt in geo.get("points", [])]
        holes = [[pt["x"], pt["y"]] for pt in geo.get("holes", [])]
        return points, holes
    except:
        return [], []

def parse_position(pos):
    try:
        if isinstance(pos, str):
            p = json.loads(pos)
            return [p["x"], p["y"]]
        return pos
    except:
        return []

def euclidean_distance(p1, p2):
    return math.sqrt((p1[0]-p2[0])**2 + (p1[1]-p2[1])**2)

def convert_edges_to_list(edge_dict, vertex_positions):
    edge_list = []
    seen_pairs = set()
    edge_counter = 1
    for from_node, to_nodes in edge_dict.items():
        for to_node in to_nodes:
            key = tuple(sorted([from_node, to_node]))
            if key in seen_pairs:
                continue
            seen_pairs.add(key)

            pos1 = vertex_positions.get(key[0])
            pos2 = vertex_positions.get(key[1])

            length = None
            if pos1 is not None and pos2 is not None:
                length = euclidean_distance(pos1, pos2)

            edge_list.append({
                "id": f"edge:{edge_counter:03d}",
                "v1": key[0],
                "v2": key[1],
                "width": None,
                "length": length,
                "count": None
            })
            edge_counter += 1

    return edge_list

def convert_passages(passages, vertex_positions):
    result = []
    for pas in passages:
        try:
            vertices = json.loads(pas.get("vertices", "[]"))
            if isinstance(vertices, list) and len(vertices) >= 2:
                v1, v2 = vertices[0], vertices[1]
                pos1 = vertex_positions.get(v1)
                pos2 = vertex_positions.get(v2)
                
                length = None
                if pos1 is not None and pos2 is not None:
                    length = math.sqrt((pos1[0]-pos2[0])**2 + (pos1[1]-pos2[1])**2)
                
                result.append({
                    "v1": v1,
                    "v2": v2,
                    "type": pas.get("type"),
                    "width": None,
                    "length": length,
                    "count": None
                })
        except:
            continue
    return result


def convert_json(data):
    building = data["map"]["building"]
    new_building = {
        "id": building.get("id"),
        "userId": building.get("userId"),
        "name": building.get("name"),
        "floors": [],
        "passages": []
    }

    # 先构建全楼宇顶点坐标字典，用于passages计算
    vertex_positions = {}
    for floor in building.get("floors", []):
        for vertex in floor.get("road", {}).get("vertices", []):
            vid = vertex.get("id")
            pos = parse_position(vertex.get("position"))
            vertex_positions[vid] = pos

    # passages转换，传入顶点坐标字典
    new_building["passages"] = convert_passages(building.get("passages", []), vertex_positions)

    for floor in building.get("floors", []):
        plane_str = floor.get("object", {}).get("plane_geometry", "")
        points, holes = parse_geometry(plane_str)

        blocks = []
        for block in floor.get("blocks", []):
            block_obj = block.get("object", {})
            blk_pts_str = block_obj.get("points", "[]")
            blocks.append({
                "id": block.get("id"),
                "points": parse_points(blk_pts_str),
                "rgb": block_obj.get("color"),
                "opacity": block_obj.get("opacity"),
                "height": block_obj.get("depth")
            })

        vertices = []
        for vertex in floor.get("road", {}).get("vertices", []):
            vertex_obj = vertex.get("object", {})
            pos = parse_position(vertex.get("position"))
            vid = vertex.get("id")
            vertices.append({
                "id": vid,
                "uid": vertex.get("userId"),
                "name": vertex.get("name"),
                "position": pos,
                "rgb": vertex_obj.get("color"),
                "levels": {
                    "level1": vertex_obj.get("type"),
                    "level2": None,
                    "level3": None
                }
            })

        edge_dict = floor.get("road", {}).get("edges", {})
        edge_list = convert_edges_to_list(edge_dict, vertex_positions)

        new_building["floors"].append({
            "id": floor.get("id"),
            "userId": floor.get("userId"),
            "name": floor.get("name"),
            "points": points,
            "holes": holes,
            "blocks": blocks,
            "vertices": vertices,
            "edges": edge_list,
            "grids": [],
            "devices": []
        })

    return {"map": {"building": new_building}}


# In[14]:


# 调用
input_path = r"C:\Users\<USER>\Desktop\疏散指示系统\管理学院_编辑版2222.json"
output_path = r"C:\Users\<USER>\Desktop\疏散指示系统\管理学院_简化_结构修改版.json"

with open(input_path, "r", encoding="utf-8") as f:
    raw_data = json.load(f)

result = convert_json(raw_data)

with open(output_path, "w", encoding="utf-8") as f:
    json.dump(result, f, ensure_ascii=False, indent=2)


#   

#    

#   

# ### grids添加

# In[56]:


def calculate_bounding_box_only_outline(points):
    if not points:
        return 0, 0, 0, 0
    xs = [p[0] for p in points]
    ys = [p[1] for p in points]
    return min(xs), min(ys), max(xs), max(ys)

def create_grid_with_data(points, floor_id, cell_size=1.0, margin=0.0, init_value=0):
    if not points:
        return None

    min_x, min_y, max_x, max_y = calculate_bounding_box_only_outline(points)

    origin_x = min_x - margin
    origin_y = min_y - margin

    width = (max_x - min_x) + 2 * margin
    height = (max_y - min_y) + 2 * margin

    cols = math.ceil(width / cell_size)
    rows = math.ceil(height / cell_size)

    origin = [origin_x, origin_y]

    # 初始化 data 矩阵，元素是数值（热力值等）
    data = [[init_value for _ in range(cols)] for _ in range(rows)]

    return {
        "id": f"grid:auto:low{floor_id}",
        "resolution": "low",
        "origin": origin,
        "cellSize": cell_size,
        "rows": rows,
        "cols": cols,
        "data": data,
        # 新增 cells 稀疏信息列表，暂空
        "cells": []
    }

def get_block_polygons(floor):
    block_polygons = {}
    for block in floor.get("blocks", []):
        pts = block.get("points", [])
        if len(pts) >= 3:
            poly = Polygon(pts)
            if poly.is_valid and not poly.is_empty:
                block_polygons[block["id"]] = poly
    return block_polygons

def analyze_grid_cells_sparse(floor, overlap_threshold=1e-6):
    block_polygons = get_block_polygons(floor)
    grids = floor.get("grids", [])
    if not block_polygons or not grids:
        return

    for grid in grids:
        origin = grid["origin"]
        cell_size = grid["cellSize"]
        rows = grid["rows"]
        cols = grid["cols"]
        ox, oy = origin

        cells_list = []

        for row in range(rows):
            for col in range(cols):
                cx = ox + col * cell_size
                cy = oy + row * cell_size
                cell_poly = box(cx, cy, cx + cell_size, cy + cell_size)
                area_total = cell_poly.area

                overlap_area = {}
                for block_id, poly in block_polygons.items():
                    inter = cell_poly.intersection(poly)
                    if not inter.is_empty:
                        overlap_area[block_id] = inter.area

                sum_overlap = sum(overlap_area.values())
                empty_area = max(0, area_total - sum_overlap)
                if empty_area > 0:
                    overlap_area["nothing"] = empty_area

                # 过滤重合面积较小的（0或小于阈值）
                filtered = {k: v for k, v in overlap_area.items() if v > overlap_threshold}
                if not filtered or (len(filtered) == 1 and "nothing" in filtered):
                    # 只有空白或无覆盖，跳过，不加入 cells
                    continue

                sorted_blocks = sorted(filtered.items(), key=lambda x: -x[1])

                level1_val = sorted_blocks[0][0] if len(sorted_blocks) > 0 else None
                level2_val = sorted_blocks[1][0] if len(sorted_blocks) > 1 else None
                level3_val = sorted_blocks[2][0] if len(sorted_blocks) > 2 else None

                cell_info = {
                    "row": row,
                    "col": col,
                    "level1": level1_val,
                    "level2": level2_val,
                    "level3": level3_val,
                    # 如果你需要，也可以加入数值字段
                    "value": grid["data"][row][col] if "data" in grid else None
                }
                cells_list.append(cell_info)

        grid["cells"] = cells_list

def add_grids_with_data_to_result(result_json, cell_size=1.0, margin=0.0, init_value=0):
    floors = result_json.get("map", {}).get("building", {}).get("floors", [])
    for floor in floors:
        points = floor.get("points", [])
        floor_id = floor.get("id")
        grid = create_grid_with_data(points, floor_id, cell_size, margin, init_value)
        if grid:
            floor["grids"] = [grid]
            analyze_grid_cells_sparse(floor)
        else:
            floor["grids"] = []


# In[57]:


# 1. 原始文件路径
input_path = r"C:\Users\<USER>\Desktop\疏散指示系统\管理学院_编辑版2222.json"
output_path = r"C:\Users\<USER>\Desktop\疏散指示系统\管理学院_添加grid后.json"

# 2. 读取原始数据并转换
with open(input_path, "r", encoding="utf-8") as f:
    raw_data = json.load(f)

# 3. 结构简化 + 添加网格
result = convert_json(raw_data)
add_grids_with_data_to_result(result, cell_size=2.0, margin=0.1, init_value=0)

# 4. 保存结果
with open(output_path, "w", encoding="utf-8") as f:
    json.dump(result, f, ensure_ascii=False, indent=2)


# In[ ]:





# In[16]:


def assign_belonging_blocks_to_edges(input_path, output_path):
    with open(input_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    floors = data.get("map", {}).get("building", {}).get("floors", [])
    
    for floor in floors:
        # 构建 block 多边形
        block_polygons = {}
        for block in floor.get("blocks", []):
            pts = block.get("points", [])
            if len(pts) >= 3:
                try:
                    poly = Polygon(pts)
                    if poly.is_valid and not poly.is_empty:
                        block_polygons[block["id"]] = poly
                except:
                    continue

        # 构建顶点位置索引
        vertex_pos = {v["id"]: v["position"] for v in floor.get("vertices", [])}

        # 分配 belonging_block 给每条边
        for edge in floor.get("edges", []):
            v1, v2 = edge.get("v1"), edge.get("v2")
            p1 = Point(vertex_pos.get(v1, []))
            p2 = Point(vertex_pos.get(v2, []))

            related_blocks = []
            for block_id, poly in block_polygons.items():
                if poly.contains(p1) or poly.contains(p2):
                    related_blocks.append(block_id)

            if not related_blocks:
                edge["belonging_block"] = None
            elif len(related_blocks) == 1:
                edge["belonging_block"] = related_blocks[0]
            else:
                edge["belonging_block"] = related_blocks  # 多个 block，保存为列表

    # 写入结果
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

    print(f"✅ 处理完成，结果已保存至：{output_path}")


# In[17]:


input_file = r"C:\Users\<USER>\Desktop\疏散指示系统\管理学院_添加grid后.json"
output_file = r"C:\Users\<USER>\Desktop\疏散指示系统\管理学院_添加belonging_block后.json"

assign_belonging_blocks_to_edges(input_file, output_file)


# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:





#   

#   

#   

# ### 地图绘制

# 基础版

# In[19]:


def plot_floor(floor):
    plt.figure(figsize=(10, 10))

    # 楼层边界（黑线）
    outline = floor.get("points", [])
    if outline:
        xs, ys = zip(*outline + [outline[0]])
        plt.plot(xs, ys, color='black', linewidth=2, label='Floor Outline')

    # block 区域（灰色填充）
    for block in floor.get("blocks", []):
        pts = block.get("points", [])
        if pts:
            xs, ys = zip(*pts + [pts[0]])
            plt.fill(xs, ys, color='lightgray', alpha=0.6, edgecolor='gray')

    # 顶点类型颜色映射
    type_color_map = {
        "vertex_block": "orange",
        "vertex_road": "green",
        "vertex_passage": "blue",
        "vertex_marker": "purple"
    }

    # 顶点着色（按类型上色）
    drawn_types = set()
    for v in floor.get("vertices", []):
        pos = v.get("position")
        if not pos:
            continue
        vtype = v.get("levels", {}).get("level1", "unknown")
        color = type_color_map.get(vtype, "gray")
        label = vtype if vtype not in drawn_types else None
        plt.scatter(pos[0], pos[1], color=color, s=20, label=label)
        drawn_types.add(vtype)

    # 顶点位置字典
    vertex_dict = {v["id"]: v["position"] for v in floor.get("vertices", []) if "id" in v and "position" in v}

    # 绘制边（一次性加图例）
    drawn_edge = False
    for edge in floor.get("edges", []):
        v1 = edge.get("v1")
        v2 = edge.get("v2")
        if v1 and v2:
            pos1 = vertex_dict.get(v1)
            pos2 = vertex_dict.get(v2)
            if pos1 and pos2:
                xs, ys = zip(pos1, pos2)
                label = "Edge" if not drawn_edge else None
                plt.plot(xs, ys, color='blue', linewidth=2, label=label)
                drawn_edge = True

    plt.title(f'Floor: {floor.get("name")}')
    plt.gca().set_aspect('equal')
    plt.legend()
    plt.tight_layout()
    plt.show()


# ===== 主流程 =====
json_path = r"C:\Users\<USER>\Desktop\疏散指示系统\管理学院_简化_结构修改版.json"
with open(json_path, "r", encoding="utf-8") as f:
    data = json.load(f)

floors = data["map"]["building"]["floors"]

# 提取 F2 层
f2 = next((f for f in floors if f.get("name") == "F2"), None)
if not f2:
    raise ValueError("未找到名为 F2 的楼层")

# 绘图
plot_floor(f2)


# In[18]:


def plot_floor(floor):
    plt.figure(figsize=(10, 10))

    # 楼层边界（黑线）
    outline = floor.get("points", [])
    if outline:
        xs, ys = zip(*outline + [outline[0]])
        plt.plot(xs, ys, color='black', linewidth=2, label='Floor Outline')

    # block 区域（灰色填充）
    for block in floor.get("blocks", []):
        pts = block.get("points", [])
        if pts:
            xs, ys = zip(*pts + [pts[0]])
            plt.fill(xs, ys, color='lightgray', alpha=0.6, edgecolor='gray')

    # 顶点类型颜色映射
    type_color_map = {
        "vertex_block": "orange",
        "vertex_road": "green",
        "vertex_passage": "blue",
        "vertex_marker": "purple"
    }

    # 顶点着色（按类型上色）
    drawn_types = set()
    for v in floor.get("vertices", []):
        pos = v.get("position")
        if not pos:
            continue
        vtype = v.get("levels", {}).get("level1", "unknown")
        color = type_color_map.get(vtype, "gray")
        label = vtype if vtype not in drawn_types else None
        plt.scatter(pos[0], pos[1], color=color, s=20, label=label)
        drawn_types.add(vtype)

    # 顶点位置字典
    vertex_dict = {v["id"]: v["position"] for v in floor.get("vertices", []) if "id" in v and "position" in v}

    # ✅ 绘制边，按 belonging_block 是否为空分色
    for edge in floor.get("edges", []):
        v1 = edge.get("v1")
        v2 = edge.get("v2")
        if v1 and v2:
            pos1 = vertex_dict.get(v1)
            pos2 = vertex_dict.get(v2)
            if pos1 and pos2:
                xs, ys = zip(pos1, pos2)
                bb = edge.get("belonging_block")
                if bb is None:
                    color = "red"     # 未归属
                    label = "Unassigned Edge"
                else:
                    color = "green"   # 已归属
                    label = "Assigned Edge"

                # 仅首次添加图例
                if not plt.gca().get_legend_handles_labels()[1].count(label):
                    plt.plot(xs, ys, color=color, linewidth=2, label=label)
                else:
                    plt.plot(xs, ys, color=color, linewidth=2)

    plt.title(f'Floor: {floor.get("name")}')
    plt.gca().set_aspect('equal')
    plt.legend()
    plt.tight_layout()
    plt.show()
    
    
    
# ===== 主流程 =====
json_path = r"C:\Users\<USER>\Desktop\疏散指示系统\管理学院_添加belonging_block后.json"
with open(json_path, "r", encoding="utf-8") as f:
    data = json.load(f)

floors = data["map"]["building"]["floors"]

# 提取 F2 层
f2 = next((f for f in floors if f.get("name") == "F2"), None)
if not f2:
    raise ValueError("未找到名为 F2 的楼层")

# 绘图
plot_floor(f2)


# 热力图版

# In[54]:


import matplotlib.pyplot as plt
import matplotlib.patches as patches

def plot_floor(floor, show_grid=True):
    plt.figure(figsize=(10, 10))

    # 楼层边界（黑线）
    outline = floor.get("points", [])
    if outline:
        xs, ys = zip(*outline + [outline[0]])
        plt.plot(xs, ys, color='black', linewidth=2, label='Floor Outline')

    # block 区域（灰色填充）
    for block in floor.get("blocks", []):
        pts = block.get("points", [])
        if pts:
            xs, ys = zip(*pts + [pts[0]])
            plt.fill(xs, ys, color='lightgray', alpha=0.6, edgecolor='gray')

    # 顶点类型颜色映射
    type_color_map = {
        "vertex_block": "orange",
        "vertex_road": "green",
        "vertex_passage": "blue",
        "vertex_marker": "purple"
    }

    # 顶点着色（按类型上色）
    drawn_types = set()
    for v in floor.get("vertices", []):
        pos = v.get("position")
        if not pos:
            continue
        vtype = v.get("levels", {}).get("level1", "unknown")
        color = type_color_map.get(vtype, "gray")
        label = vtype if vtype not in drawn_types else None
        plt.scatter(pos[0], pos[1], color=color, s=20, label=label)
        drawn_types.add(vtype)

    # 顶点位置字典
    vertex_dict = {v["id"]: v["position"] for v in floor.get("vertices", []) if "id" in v and "position" in v}

    # 绘制边
    drawn_edge = False
    for edge in floor.get("edges", []):
        v1 = edge.get("v1")
        v2 = edge.get("v2")
        if v1 and v2:
            pos1 = vertex_dict.get(v1)
            pos2 = vertex_dict.get(v2)
            if pos1 and pos2:
                xs, ys = zip(pos1, pos2)
                label = "Edge" if not drawn_edge else None
                plt.plot(xs, ys, color='blue', linewidth=2, label=label)
                drawn_edge = True

   # ================= 添加 Grid 可视化 =================
    if show_grid:
        for grid in floor.get("grids", []):
            origin = grid.get("origin", [0, 0])
            rows = grid.get("rows", 0)
            cols = grid.get("cols", 0)
            cell_size = grid.get("cellSize", 1.0)
            level1 = grid.get("target", {}).get("level1", [])

            ox, oy = origin

            # 画网格外框
            rect = patches.Rectangle(
                (ox, oy),
                cols * cell_size,
                rows * cell_size,
                linewidth=1,
                edgecolor='red',
                facecolor='none',
                linestyle='--',
                label='Grid Boundary'
            )
            plt.gca().add_patch(rect)

            # 画网格线（可选，如果太密可以注释掉）
            for row in range(rows + 1):
                plt.plot([ox, ox + cols * cell_size], [oy + row * cell_size, oy + row * cell_size], color='red', linewidth=0.3, linestyle='--')
            for col in range(cols + 1):
                plt.plot([ox + col * cell_size, ox + col * cell_size], [oy, oy + rows * cell_size], color='red', linewidth=0.3, linestyle='--')

            # 只填充 level1 非空且不为 "nothing" 的格子
            for row in range(rows):
                for col in range(cols):
                    if not level1:
                        continue
                    val = level1[row][col]
                    if val is not None and val != "nothing":
                        cx = ox + col * cell_size
                        cy = oy + row * cell_size
                        cell_rect = patches.Rectangle(
                            (cx, cy),
                            cell_size,
                            cell_size,
                            linewidth=0.3,
                            edgecolor='red',
                            facecolor='orange',
                            alpha=0.5
                        )
                        plt.gca().add_patch(cell_rect)

            break  # 假设只有一个 grid
            
    plt.title(f'Floor: {floor.get("name")}')
    plt.gca().set_aspect('equal')
    plt.legend()
    plt.tight_layout()
    plt.show()


# In[60]:


# 读取原始数据并转换
input_path = r"C:\Users\<USER>\Desktop\疏散指示系统\管理学院_编辑版2222.json"

with open(input_path, "r", encoding="utf-8") as f:
    raw_data = json.load(f)

result = convert_json(raw_data)
add_grids_with_data_to_result(result, cell_size=2.0, margin=0.0, init_value=0)

# 提取并绘制 F2
floor_f2 = next((f for f in result["map"]["building"]["floors"] if f.get("name") == "F2"), None)
if floor_f2:
    plot_floor_with_cells(floor_f2)
else:
    print("未找到 F2 楼层")



# In[ ]:





# In[ ]:




