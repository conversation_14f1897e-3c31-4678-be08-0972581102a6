import requests
import json

# 设置目标 URL 和端口
url = 'http://************:5005'
# url = 'http://localhost:5005'
# 创建一个示例的 JSON 数据
data = {
    "message": "手动报警器触发",
    "recipients": ["2015559", "2515523"],
    "alert": True
}

# 发送 POST 请求
response = requests.post(url, json=data)

# 打印响应
print(f"响应状态码: {response.status_code}")
print(f"响应内容: {response.text}")

# 如果响应成功，还可以查询报警器状态
status_url = f'{url}/status'
status_response = requests.get(status_url)
print(f"当前报警器状态: {status_response.text}")
