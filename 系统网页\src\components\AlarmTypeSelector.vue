<script setup>
import { ref } from 'vue';

// 报警类型选项
const alarmTypes = [
  { value: '火灾', label: '火灾' },
  { value: '人群骚乱', label: '人群骚乱' },
  { value: '地震', label: '地震' },
  { value: '袭击事件', label: '袭击事件' },
  { value: 'custom', label: '自定义内容' }
];

// 组件状态
const selectedType = ref('');
const customContent = ref('');
const isVisible = ref(false);

// 事件定义
const emit = defineEmits(['confirm', 'cancel']);

// 显示弹窗
const show = () => {
  isVisible.value = true;
  selectedType.value = '';
  customContent.value = '';
};

// 隐藏弹窗
const hide = () => {
  isVisible.value = false;
  selectedType.value = '';
  customContent.value = '';
};

// 确认选择
const confirmSelection = () => {
  if (!selectedType.value) {
    alert('请选择报警类型');
    return;
  }
  
  if (selectedType.value === 'custom' && !customContent.value.trim()) {
    alert('请输入自定义内容');
    return;
  }
  
  const alarmContent = selectedType.value === 'custom' 
    ? customContent.value.trim() 
    : selectedType.value;
    
  emit('confirm', alarmContent);
  hide();
};

// 取消选择
const cancelSelection = () => {
  emit('cancel');
  hide();
};

// 暴露方法给父组件
defineExpose({
  show,
  hide
});
</script>

<template>
  <!-- 报警类型选择弹窗 -->
  <div v-if="isVisible" class="alarm-selector-overlay">
    <div class="alarm-selector-form">
      <!-- 弹窗头部 -->
      <div class="form-header">
        <h3>选择报警类型</h3>
        <button class="close-btn" @click="cancelSelection">×</button>
      </div>
      
      <!-- 弹窗内容 -->
      <div class="form-body">
        <div class="form-group">
          <label>报警类型：</label>
          <div class="alarm-type-options">
            <div 
              v-for="type in alarmTypes" 
              :key="type.value"
              class="alarm-type-option"
              :class="{ active: selectedType === type.value }"
              @click="selectedType = type.value"
            >
              <input 
                type="radio" 
                :value="type.value" 
                v-model="selectedType"
                :id="'alarm-' + type.value"
              />
              <label :for="'alarm-' + type.value">{{ type.label }}</label>
            </div>
          </div>
        </div>
        
        <!-- 自定义内容输入框 -->
        <div v-if="selectedType === 'custom'" class="form-group">
          <label>自定义内容：</label>
          <textarea 
            v-model="customContent"
            placeholder="请输入具体的报警内容..."
            maxlength="100"
          ></textarea>
          <div class="char-count">{{ customContent.length }}/100</div>
        </div>
      </div>
      
      <!-- 弹窗底部 -->
      <div class="form-footer">
        <button class="form-btn cancel" @click="cancelSelection">取消</button>
        <button class="form-btn confirm" @click="confirmSelection">确认触发</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 弹窗遮罩层 */
.alarm-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* 弹窗主体 */
.alarm-selector-form {
  background-color: #0d2b55;
  border: 1px solid #1e3a5f;
  border-radius: 8px;
  width: 450px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

/* 弹窗头部 */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #1e3a5f;
}

.form-header h3 {
  margin: 0;
  color: white;
  font-size: 1.1rem;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

/* 弹窗内容 */
.form-body {
  padding: 15px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: white;
  font-size: 0.9rem;
  font-weight: bold;
}

/* 报警类型选项样式 */
.alarm-type-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.alarm-type-option {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #1e3a5f;
  border: 1px solid #2997e3;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.alarm-type-option:hover {
  background-color: #2a4a6b;
  border-color: #40a9ff;
}

.alarm-type-option.active {
  background-color: #1890ff;
  border-color: #40a9ff;
}

.alarm-type-option input[type="radio"] {
  margin-right: 8px;
  accent-color: #1890ff;
}

.alarm-type-option label {
  margin: 0;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  flex: 1;
}

/* 自定义内容输入框 */
.form-group textarea {
  width: 100%;
  padding: 8px;
  background-color: #1e3a5f;
  color: white;
  border: 1px solid #2997e3;
  border-radius: 4px;
  font-size: 0.9rem;
  box-sizing: border-box;
  height: 80px;
  resize: vertical;
  font-family: inherit;
}

.form-group textarea:focus {
  outline: none;
  border-color: #40a9ff;
}

.char-count {
  text-align: right;
  font-size: 0.75rem;
  color: #8c8c8c;
  margin-top: 4px;
}

/* 弹窗底部 */
.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px;
  border-top: 1px solid #1e3a5f;
}

.form-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.form-btn.cancel {
  background-color: #8c8c8c;
  color: white;
}

.form-btn.cancel:hover {
  background-color: #999;
}

.form-btn.confirm {
  background-color: #ff4d4f;
  color: white;
}

.form-btn.confirm:hover {
  background-color: #ff7875;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alarm-selector-form {
    width: 95vw;
    margin: 10px;
  }
  
  .alarm-type-options {
    gap: 6px;
  }
  
  .alarm-type-option {
    padding: 8px;
  }
}
</style>
